// src/services/batchResourceUpdateService.ts
import { UserWallet } from '../models/UserWallet';
import { DeliveryLine } from '../models/DeliveryLine';
import { FarmPlot } from '../models/FarmPlot';
import { WalletHistory } from '../models/WalletHistory';
import { sequelize } from '../config/db';
import { Transaction, Op } from 'sequelize';
import BigNumber from 'bignumber.js';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import dayjs from 'dayjs';

// GEM限制常量
const GEM_LIMITS = {
  SINGLE_UPDATE_MAX: 10000,      // 单次更新上限
  DAILY_CUMULATIVE_MAX: 50000,   // 每日累计上限
  WALLET_TOTAL_MAX: 1000000      // 钱包总量上限
};

/**
 * 牛奶操作对象
 */
export interface MilkOperations {
  produce?: number;  // 农场生产量
  consume?: number;  // 出货线消耗量
}

/**
 * 批量资源更新请求参数
 */
export interface BatchResourceUpdateRequest {
  gemRequest?: number;                 // 前端请求的GEM增量（只能为非负数）
  milkOperations?: MilkOperations;     // 前端请求的牛奶操作（只支持对象形式）
}

/**
 * 批量资源更新响应数据
 */
export interface BatchResourceUpdateResponse {
  success: boolean;
  data: {
    beforeUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string | null;
    };
    afterUpdate: {
      gem: number;
      pendingMilk: number;
      lastActiveTime: string;
    };
    changes: {
      usedSystemCalculation?: boolean; // 是否使用了系统计算值（因请求不合理）
      systemCalculationReason?: string; // 使用系统计算的原因
      productionRates: {
        farmMilkPerCycle: number; // 农场每个生产周期的牛奶产量
        deliveryBlockUnit: number; // 出货线每个方块消耗的牛奶数量
        deliveryBlockPrice: number; // 出货线每个方块的GEM价格
        timeElapsedSeconds: number; // 实际经过的秒数
      };
      details: {
        gem: {
          increased: number; // GEM增加量
        };
        milk: {
          increased: number; // 牛奶增加量
          decreased: number; // 牛奶减少量
        };
      };
    };
    timestamp: string;
  };
  message: string;
}

/**
 * 批量资源更新服务类
 */
export class BatchResourceUpdateService {
  /**
   * 验证请求参数
   */
  private static validateRequest(request: BatchResourceUpdateRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 如果没有提供任何参数，允许无参数调用（系统自动计算）
    // 这种情况下会根据时间自动计算资源

    // 验证 gemRequest - 只能为非负数（GEM只会增加不会减少）
    if (request.gemRequest !== undefined) {
      if (typeof request.gemRequest !== 'number' || isNaN(request.gemRequest)) {
        errors.push('gemRequest 必须是有效的数值');
      } else if (request.gemRequest < 0) {
        errors.push('gemRequest 必须为非负数（GEM只会增加不会减少）');
      }
    }

    // 验证 milkOperations - 只支持对象形式
    if (request.milkOperations !== undefined) {
      if (typeof request.milkOperations === 'object' && request.milkOperations !== null) {
        // 对象形式：验证 produce 和 consume 字段
        const milkOps = request.milkOperations as MilkOperations;

        if (milkOps.produce !== undefined) {
          if (typeof milkOps.produce !== 'number' || isNaN(milkOps.produce) || milkOps.produce < 0) {
            errors.push('milkOperations.produce 必须是非负数');
          }
        }

        if (milkOps.consume !== undefined) {
          if (typeof milkOps.consume !== 'number' || isNaN(milkOps.consume) || milkOps.consume < 0) {
            errors.push('milkOperations.consume 必须是非负数');
          }
        }

        // 至少需要提供一个操作
        if (milkOps.produce === undefined && milkOps.consume === undefined) {
          errors.push('milkOperations 对象必须包含 produce 或 consume 字段');
        }
      } else {
        errors.push('milkOperations 必须是包含 produce/consume 字段的对象');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }



  /**
   * 计算用户今日已累计的GEM更新量
   */
  private static async calculateDailyGemUpdates(walletId: number, transaction: Transaction): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dailyGemHistory = await WalletHistory.findAll({
      where: {
        walletId,
        currency: 'GEM',
        action: 'IN',
        category: 'BATCH_UPDATE',
        createdAt: {
          [Op.gte]: today,
          [Op.lt]: tomorrow
        }
      },
      transaction
    });

    let totalDailyGem = 0;
    dailyGemHistory.forEach(record => {
      totalDailyGem += record.amount || 0;
    });

    return formatToThreeDecimalsNumber(totalDailyGem);
  }

  /**
   * 计算农场区块的总产能（每秒）- 保留用于兼容性
   */
  private static async calculateFarmProductionCapacity(walletId: number, transaction: Transaction): Promise<number> {
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    let totalProductionPerSecond = 0;
    for (const plot of farmPlots) {
      // 每秒产量 = 牛奶产量 ÷ 产出时间
      const plotProductionPerSecond = plot.milkProduction / plot.productionSpeed;
      totalProductionPerSecond += plotProductionPerSecond;
    }

    return formatToThreeDecimalsNumber(totalProductionPerSecond);
  }

  /**
   * 基于周期计算农场实际产量（正确的逻辑）
   * 现在支持VIP生产速度加成
   */
  private static async calculateFarmProductionByCycles(walletId: number, timeElapsedSeconds: number, transaction: Transaction): Promise<number> {
    // 获取VIP效果
    const iapController = require('../controllers/iapController').default;
    const vipEffects = await iapController.getVipEffects(walletId);
    const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier || 1; // VIP 30% 牧场区生产速度加成

    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    let totalProduction = 0;
    for (const plot of farmPlots) {
      // 应用VIP生产速度加成：实际速度 = 基础速度 ÷ 加成倍率
      const actualProductionSpeed = plot.productionSpeed / productionSpeedMultiplier;
      // 计算完整周期数：时间 ÷ 实际生产速度
      const completeCycles = Math.floor(timeElapsedSeconds / actualProductionSpeed);
      // 实际产量 = 完整周期数 × 每周期产量
      const plotProduction = completeCycles * plot.milkProduction;
      totalProduction += plotProduction;
    }

    return formatToThreeDecimalsNumber(totalProduction);
  }

  /**
   * 基于周期计算出货线实际处理量（正确的逻辑）
   * 现在支持VIP和加速道具效果
   */
  private static async calculateDeliveryProcessingByCycles(
    deliveryLine: any,
    timeElapsedSeconds: number,
    walletId: number
  ): Promise<{ milkConsumed: number; gemProduced: number }> {
    if (!deliveryLine) {
      return { milkConsumed: 0, gemProduced: 0 };
    }

    // 获取VIP和加速道具效果
    const iapController = require('../controllers/iapController').default;
    const vipEffects = await iapController.getVipEffects(walletId);
    const boosterEffects = await iapController.getBoosterEffects(walletId);

    // 计算加成效果
    const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1); // VIP 30% + Speed Boost 出货线速度加成
    const blockPriceMultiplier = vipEffects.blockPriceMultiplier || 1; // VIP 20% 出货线价格加成

    // 应用出货速度加成：实际速度 = 基础速度 ÷ 加成倍率
    const actualDeliverySpeed = deliveryLine.deliverySpeed / deliverySpeedMultiplier;
    // 应用价格加成：实际价格 = 基础价格 × 价格倍率
    const actualBlockPrice = deliveryLine.blockPrice * blockPriceMultiplier;

    // 计算完整周期数：时间 ÷ 实际出货速度
    const completeCycles = Math.floor(timeElapsedSeconds / actualDeliverySpeed);

    // 实际消耗和产出 = 完整周期数 × 每周期量
    const milkConsumed = completeCycles * deliveryLine.blockUnit;
    const gemProduced = completeCycles * actualBlockPrice;

    return {
      milkConsumed: formatToThreeDecimalsNumber(milkConsumed),
      gemProduced: formatToThreeDecimalsNumber(gemProduced)
    };
  }

  /**
   * 协调计算农场和出货线的实际产出（考虑时间依赖关系）
   * 当农场生产慢时，出货线需要等待农场生产
   * 现在支持VIP和加速道具效果
   */
  private static async calculateCoordinatedProduction(
    walletId: number,
    timeElapsedSeconds: number,
    currentPendingMilk: number,
    transaction: Transaction
  ): Promise<{
    farmProduced: number;
    deliveryConsumed: number;
    gemProduced: number;
    finalPendingMilk: number
  }> {
    // 获取VIP和加速道具效果
    const iapController = require('../controllers/iapController').default;
    const vipEffects = await iapController.getVipEffects(walletId);
    const boosterEffects = await iapController.getBoosterEffects(walletId);

    // 获取农场和出货线数据
    let farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    // 如果用户没有解锁的农场区块，自动初始化一个
    if (farmPlots.length === 0) {
      const defaultFarmPlot = await FarmPlot.create({
        walletId,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        milkProduction: 1, // 1牛奶/次
        productionSpeed: 5, // 5秒/次
        barnCount: 1,
        upgradeCost: 100,
        unlockCost: 0,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      }, { transaction });

      farmPlots = [defaultFarmPlot];
    }

    const deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    // 使用时间依赖的协调计算，传入VIP和加速道具效果
    const result = this.calculateTimeBasedCoordination(
      farmPlots,
      deliveryLine,
      timeElapsedSeconds,
      currentPendingMilk,
      vipEffects,
      boosterEffects
    );

    return {
      farmProduced: formatToThreeDecimalsNumber(result.farmProduced),
      deliveryConsumed: formatToThreeDecimalsNumber(result.deliveryConsumed),
      gemProduced: formatToThreeDecimalsNumber(result.gemProduced),
      finalPendingMilk: formatToThreeDecimalsNumber(result.finalPendingMilk)
    };
  }

  /**
   * 获取基础配置信息（不受时间影响的游戏机制参数）
   */
  private static async getBasicConfigurationRates(
    walletId: number,
    transaction: Transaction,
    timeElapsedSeconds: number
  ): Promise<{
    farmMilkPerCycle: number;
    deliveryBlockUnit: number;
    deliveryBlockPrice: number;
    timeElapsedSeconds: number;
  }> {
    // 获取农场区数据（只获取已解锁的）
    let farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    // 如果用户没有解锁的农场区块，自动初始化一个
    if (farmPlots.length === 0) {
      const defaultFarmPlot = await FarmPlot.create({
        walletId,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        milkProduction: 1, // 1牛奶/次
        productionSpeed: 5, // 5秒/次
        barnCount: 1,
        upgradeCost: 100,
        unlockCost: 0,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      }, { transaction });

      farmPlots = [defaultFarmPlot];
    }

    // 获取出货线数据 - 如果不存在则自动初始化
    let deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 如果用户没有出货线，自动初始化一个
    if (!deliveryLine) {
      deliveryLine = await DeliveryLine.create({
        walletId,
        level: 1,
        deliverySpeed: 5, // 5秒/次
        blockUnit: 5, // 5牛奶/方块
        blockPrice: 5, // 5 GEM/方块
        upgradeCost: 500, // 初始升级费用
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      }, { transaction });
    }

    // 计算农场每个周期的总产量（所有已解锁农场区的产量之和）
    let totalFarmMilkPerCycle = 0;
    for (const plot of farmPlots) {
      totalFarmMilkPerCycle += plot.milkProduction;
    }

    return {
      farmMilkPerCycle: formatToThreeDecimalsNumber(totalFarmMilkPerCycle),
      deliveryBlockUnit: formatToThreeDecimalsNumber(deliveryLine.blockUnit),
      deliveryBlockPrice: formatToThreeDecimalsNumber(deliveryLine.blockPrice),
      timeElapsedSeconds: timeElapsedSeconds
    };
  }

  /**
   * 基于时间的协调计算 - 考虑农场和出货线的时间依赖关系
   * 当农场生产慢时，出货线必须等待农场生产
   * 现在支持VIP和加速道具效果
   */
  private static calculateTimeBasedCoordination(
    farmPlots: any[],
    deliveryLine: any,
    timeElapsedSeconds: number,
    initialPendingMilk: number,
    vipEffects: any,
    boosterEffects: any
  ): {
    farmProduced: number;
    deliveryConsumed: number;
    gemProduced: number;
    finalPendingMilk: number;
  } {
    // let currentTime = 0; // 暂时不需要跟踪当前时间
    let currentPendingMilk = initialPendingMilk;
    let totalFarmProduced = 0;
    let totalDeliveryConsumed = 0;
    let totalGemProduced = 0;

    // 计算加成效果
    const productionSpeedMultiplier = vipEffects.productionSpeedMultiplier || 1; // VIP 30% 牧场区生产速度加成
    const deliverySpeedMultiplier = (vipEffects.deliverySpeedMultiplier || 1) * (boosterEffects.speedMultiplier || 1); // VIP 30% + Speed Boost 出货线速度加成
    const blockPriceMultiplier = vipEffects.blockPriceMultiplier || 1; // VIP 20% 出货线价格加成

    // 创建事件队列：农场生产事件和出货线处理事件
    const events: Array<{
      time: number;
      type: 'farm' | 'delivery';
      plotIndex?: number;
      amount?: number;
      gemPrice?: number; // 添加GEM价格字段
    }> = [];

    // 添加农场生产事件（应用VIP生产速度加成）
    farmPlots.forEach((plot, index) => {
      const baseProductionSpeed = Number(plot.productionSpeed);
      const milkProduction = Number(plot.milkProduction);

      // 应用VIP生产速度加成：实际速度 = 基础速度 ÷ 加成倍率
      const actualProductionSpeed = baseProductionSpeed / productionSpeedMultiplier;

      let nextProductionTime = actualProductionSpeed;
      while (nextProductionTime <= timeElapsedSeconds) {
        events.push({
          time: nextProductionTime,
          type: 'farm',
          plotIndex: index,
          amount: milkProduction
        });
        nextProductionTime += actualProductionSpeed;
      }
    });

    // 添加出货线处理事件（应用VIP和Speed Boost出货速度加成）
    const baseDeliverySpeed = Number(deliveryLine.deliverySpeed);
    const blockUnit = Number(deliveryLine.blockUnit);
    const baseBlockPrice = Number(deliveryLine.blockPrice);

    // 应用出货速度加成：实际速度 = 基础速度 ÷ 加成倍率
    const actualDeliverySpeed = baseDeliverySpeed / deliverySpeedMultiplier;
    // 应用价格加成：实际价格 = 基础价格 × 价格倍率
    const actualBlockPrice = baseBlockPrice * blockPriceMultiplier;

    let nextDeliveryTime = actualDeliverySpeed;
    while (nextDeliveryTime <= timeElapsedSeconds) {
      events.push({
        time: nextDeliveryTime,
        type: 'delivery',
        amount: blockUnit,
        gemPrice: actualBlockPrice // 添加实际的GEM价格
      });
      nextDeliveryTime += actualDeliverySpeed;
    }

    // 按时间排序事件
    events.sort((a, b) => a.time - b.time);

    // 处理事件队列
    for (const event of events) {
      // currentTime = event.time; // 暂时不需要跟踪当前时间

      if (event.type === 'farm') {
        // 农场生产事件
        currentPendingMilk += event.amount!;
        totalFarmProduced += event.amount!;
      } else if (event.type === 'delivery') {
        // 出货线处理事件
        const requiredMilk = event.amount!;

        if (currentPendingMilk >= requiredMilk) {
          // 有足够牛奶，可以处理
          currentPendingMilk -= requiredMilk;
          totalDeliveryConsumed += requiredMilk;
          // 使用加成后的GEM价格
          const gemPrice = event.gemPrice || Number(deliveryLine.blockPrice);
          totalGemProduced += gemPrice;
        }
        // 如果牛奶不够，出货线等待（跳过这次处理）
      }
    }

    return {
      farmProduced: totalFarmProduced,
      deliveryConsumed: totalDeliveryConsumed,
      gemProduced: totalGemProduced,
      finalPendingMilk: currentPendingMilk
    };
  }

  /**
   * 计算用户的在线状态和离线时间
   */
  private static calculateUserOnlineStatus(lastActiveTime: Date | null): {
    isOnline: boolean;
    offlineTimeInSeconds: number;
    offlineTimeInHours: number;
  } {
    if (!lastActiveTime) {
      return {
        isOnline: false,
        offlineTimeInSeconds: 0,
        offlineTimeInHours: 0
      };
    }

    const now = new Date();
    const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : new Date(lastActiveTime);
    const offlineTimeInSeconds = Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000);

    // 如果在1分钟内有活动，则视为在线
    const isOnline = offlineTimeInSeconds < 60;
    const offlineTimeInHours = offlineTimeInSeconds / 3600;

    return {
      isOnline,
      offlineTimeInSeconds,
      offlineTimeInHours
    };
  }

  /**
   * 解析milkOperations参数，转换为净增量
   */
  private static parseMilkOperations(milkOperations: MilkOperations | undefined): number {
    if (milkOperations === undefined) {
      return 0;
    }

    // 对象形式：计算净增量 = produce - consume
    const produce = milkOperations.produce || 0;
    const consume = milkOperations.consume || 0;
    return produce - consume;
  }

  /**
   * 验证GEM增量是否合理
   */
  private static async validateGemIncrement(
    tempGem: number,
    timeElapsedHours: number,
    walletId: number,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{ isValid: boolean; reason?: string; adjustedTempGem: number }> {

    // 计算出货线处理能力 - 如果不存在则自动初始化
    let deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });

    // 如果用户没有出货线，自动初始化一个
    if (!deliveryLine) {
      deliveryLine = await DeliveryLine.create({
        walletId,
        level: 1,
        deliverySpeed: 5, // 5秒/次
        blockUnit: 5, // 5牛奶/方块
        blockPrice: 5, // 5 GEM/方块
        upgradeCost: 500, // 初始升级费用
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      }, { transaction });
    }

    const timeInSecondsBN = createBigNumber(timeElapsedHours).multipliedBy(3600);

    // 使用协调计算，考虑牛奶库存限制
    const timeInSeconds = formatToThreeDecimalsNumber(timeInSecondsBN);
    const currentPendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);
    const coordinatedResult = await this.calculateCoordinatedProduction(
      walletId,
      timeInSeconds,
      currentPendingMilk,
      transaction
    );
    const theoreticalGemIncrease = coordinatedResult.gemProduced;
    const maxAllowedGemIncrease = formatToThreeDecimalsNumber(createBigNumber(theoreticalGemIncrease).multipliedBy(1.5));

    // 智能验证逻辑：如果前端请求 > 实际计算的1.5倍，使用实际计算；否则使用前端请求
    if (tempGem > maxAllowedGemIncrease) {
      return {
        isValid: false,
        reason: `GEM请求量 (${tempGem.toFixed(3)}) 超出最大允许值 (${maxAllowedGemIncrease.toFixed(3)})，使用计算值`,
        adjustedTempGem: theoreticalGemIncrease
      };
    }

    // 前端请求在1.5倍误差范围内，使用前端请求的数量
    return { isValid: true, adjustedTempGem: tempGem };
  }

  /**
   * 验证牛奶操作是否合理（分别验证produce和consume）
   */
  private static async validateMilkOperations(
    milkOperations: MilkOperations | undefined,
    timeElapsedHours: number,
    walletId: number,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{
    isValid: boolean;
    reason?: string;
    adjustedProduce: number;
    adjustedConsume: number;
  }> {
    if (!milkOperations) {
      return {
        isValid: true,
        adjustedProduce: 0,
        adjustedConsume: 0
      };
    }

    const requestedProduce = milkOperations.produce || 0;
    const requestedConsume = milkOperations.consume || 0;

    // 获取协调计算结果
    const timeInSecondsBN = createBigNumber(timeElapsedHours).multipliedBy(3600);
    const timeInSeconds = formatToThreeDecimalsNumber(timeInSecondsBN);

    // 获取delivery line（如果不存在则自动初始化）
    let deliveryLine = await DeliveryLine.findOne({ where: { walletId }, transaction });
    if (!deliveryLine) {
      deliveryLine = await DeliveryLine.create({
        walletId,
        level: 1,
        deliverySpeed: 5,
        blockUnit: 5,
        blockPrice: 5,
        upgradeCost: 500,
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      }, { transaction });
    }

    const currentPendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);
    const coordinatedResult = await this.calculateCoordinatedProduction(
      walletId,
      timeInSeconds,
      currentPendingMilk,
      transaction
    );

    // 计算理论值
    const theoreticalProduce = coordinatedResult.farmProduced;
    const theoreticalConsume = coordinatedResult.deliveryConsumed;

    // 验证produce（农场生产）
    const maxAllowedProduce = formatToThreeDecimalsNumber(createBigNumber(theoreticalProduce).multipliedBy(1.5));
    let adjustedProduce = requestedProduce;
    let produceValid = true;
    let produceReason = '';

    if (requestedProduce > maxAllowedProduce) {
      produceValid = false;
      produceReason = `牛奶生产请求 (${requestedProduce.toFixed(3)}) 超出最大允许值 (${maxAllowedProduce.toFixed(3)})`;
      adjustedProduce = theoreticalProduce;
    }

    // 验证consume（出货线消耗）
    const maxAllowedConsume = formatToThreeDecimalsNumber(createBigNumber(theoreticalConsume).multipliedBy(1.5));
    let adjustedConsume = requestedConsume;
    let consumeValid = true;
    let consumeReason = '';

    if (requestedConsume > maxAllowedConsume) {
      consumeValid = false;
      consumeReason = `牛奶消耗请求 (${requestedConsume.toFixed(3)}) 超出最大允许值 (${maxAllowedConsume.toFixed(3)})`;
      adjustedConsume = theoreticalConsume;
    }

    // 组合结果
    const isValid = produceValid && consumeValid;
    const reasons = [];
    if (!produceValid) reasons.push(produceReason);
    if (!consumeValid) reasons.push(consumeReason);

    return {
      isValid,
      reason: reasons.length > 0 ? reasons.join('; ') : undefined,
      adjustedProduce,
      adjustedConsume
    };
  }


  /**
   * 基于用户lastActiveTime计算应该获得的资源上限
   */
  private static async calculateTimeBasedResourceLimits(
    walletId: number,
    lastActiveTime: Date | null,
    deliveryLine: DeliveryLine | null,
    transaction: Transaction,
    language: string = 'zh'
  ): Promise<{
    gemLimit: number;
    pendingMilkLimit: number;
    timeElapsedHours: number;
    reason: string;
  }> {
    const now = new Date();

    // 如果没有lastActiveTime，设置为当前时间（新用户）
    if (!lastActiveTime) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours: 0,
        reason: language === 'en' ? 'New user, no accumulated resources' : '新用户，无累积资源'
      };
    }

    // 计算时间差（小时）
    const lastActiveDate = new Date(lastActiveTime);
    const timeElapsedMs = now.getTime() - lastActiveDate.getTime();
    const timeElapsedHours = timeElapsedMs / (1000 * 60 * 60);

    // 如果时间差小于5秒，不给予资源
    if (timeElapsedHours < (5/3600)) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours,
        reason: language === 'en' ? 'Too soon since last update (minimum 5 seconds)' : '距离上次更新时间太短（最少5秒）'
      };
    }

    // 如果时间差超过2分钟，判定为离线，不给予资源
    if (timeElapsedHours > (2/60)) {
      return {
        gemLimit: 0,
        pendingMilkLimit: 0,
        timeElapsedHours,
        reason: '用户离线（距离上次更新超过2分钟）'
      };
    }

    // 计算资源上限（基于周期的正确计算）
    const timeElapsedSeconds = timeElapsedHours * 3600;
    const actualFarmProduction = await this.calculateFarmProductionByCycles(walletId, timeElapsedSeconds, transaction);
    const pendingMilkLimit = actualFarmProduction;

    // 计算GEM上限（基于出货线处理能力）
    // 每个用户都有出货线，如果查询失败则抛出错误
    if (!deliveryLine) {
      throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
    }

    const deliveryProcessing = await this.calculateDeliveryProcessingByCycles(deliveryLine, timeElapsedSeconds, walletId);
    const gemLimit = deliveryProcessing.gemProduced;

    const reason = language === 'en'
      ? `Based on ${timeElapsedHours.toFixed(2)} hours since last active`
      : `基于距离上次活跃${timeElapsedHours.toFixed(2)}小时`;

    return {
      gemLimit: formatToThreeDecimalsNumber(gemLimit),
      pendingMilkLimit: formatToThreeDecimalsNumber(pendingMilkLimit),
      timeElapsedHours,
      reason
    };
  }

  /**
   * 执行批量资源更新
   */
  public static async updateResources(
    userId: number,
    walletId: number,
    request: BatchResourceUpdateRequest,
    language: string = 'zh'
  ): Promise<BatchResourceUpdateResponse> {
    // 验证请求参数
    const validation = this.validateRequest(request);
    if (!validation.isValid) {
      throw new Error(`参数验证失败: ${validation.errors.join(', ')}`);
    }

    const transaction: Transaction = await sequelize.transaction();

    try {
      // 获取用户钱包
      const wallet = await UserWallet.findOne({
        where: { id: walletId, userId },
        transaction
      });

      if (!wallet) {
        await transaction.rollback();
        throw new Error('用户钱包不存在');
      }

      // 记录更新前状态（使用BigNumber保持精度）
      const beforeUpdateBN = {
        gem: createBigNumber(wallet.gem || 0),
        pendingMilk: createBigNumber(0)
      };

      const beforeUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: 0,
        lastActiveTime: wallet.lastActiveTime ? dayjs(wallet.lastActiveTime).format('YYYY-MM-DD HH:mm:ss') : null
      };

      // 获取出货线信息 - 如果不存在则自动初始化
      let deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      // 如果用户没有出货线，自动初始化一个
      if (!deliveryLine) {
        deliveryLine = await DeliveryLine.create({
          walletId,
          level: 1,
          deliverySpeed: 5, // 5秒/次
          blockUnit: 5, // 5牛奶/方块
          blockPrice: 5, // 5 GEM/方块
          upgradeCost: 500, // 初始升级费用
          lastDeliveryTime: new Date(),
          pendingMilk: 0,
          pendingBlocks: 0
        }, { transaction });
      }

      beforeUpdate.pendingMilk = formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0);
      beforeUpdateBN.pendingMilk = createBigNumber(deliveryLine.pendingMilk || 0);

      // 计算基于时间的资源上限
      const resourceLimits = await this.calculateTimeBasedResourceLimits(
        walletId,
        wallet.lastActiveTime as Date | null,
        deliveryLine,
        transaction,
        language
      );

      // 验证前端提交的资源数据
      let usedSystemCalculation = false;
      let systemCalculationReason = '';

      // 详细增减信息
      let gemIncreased = 0;
      let milkIncreased = 0;
      let milkDecreased = 0;

      // 注意：不再计算每秒速率，而是使用实际的生产量和消耗量

      // 处理前端提交的增量数据
      let finalTempGem = 0;
      let finalTempMilk = 0;

      // 验证结果变量（在更大作用域中声明）
      let gemValidation = { isValid: true, adjustedTempGem: 0 };
      let milkValidation = { isValid: true, adjustedProduce: 0, adjustedConsume: 0 };

      // 检查是否在有效时间窗口内（5秒-2分钟）
      const timeElapsedSecondsBN = createBigNumber(resourceLimits.timeElapsedHours).multipliedBy(3600);
      const timeElapsedSeconds = formatToThreeDecimalsNumber(timeElapsedSecondsBN);
      const isInValidTimeWindow = timeElapsedSeconds >= 5 && timeElapsedSeconds <= 120; // 5秒到2分钟

      // 检查是否提供了必要的参数
      if (request.gemRequest === undefined && request.milkOperations === undefined) {
        throw new Error('必须提供 gemRequest 或 milkOperations 参数');
      }

      // 提取前端请求值
      const requestedGems = request.gemRequest || 0;
      const requestedMilkNet = this.parseMilkOperations(request.milkOperations);

      if (!isInValidTimeWindow) {
        // 超出有效时间窗口，不计算任何资源
        finalTempGem = 0;
        finalTempMilk = 0;
        usedSystemCalculation = true;
        systemCalculationReason = resourceLimits.reason;
      } else {
        // 在有效时间窗口内，且前端提供了增量数据，进行验证

        // 这里先记录前端请求值，稍后会用实际计算值覆盖
        if (request.milkOperations) {
          milkIncreased = formatToThreeDecimalsNumber(request.milkOperations.produce || 0);
          milkDecreased = formatToThreeDecimalsNumber(request.milkOperations.consume || 0);
        }
        gemIncreased = formatToThreeDecimalsNumber(requestedGems); // GEM只能增加

        // 更新验证结果的初始值
        gemValidation = { isValid: true, adjustedTempGem: requestedGems };
        milkValidation = {
          isValid: true,
          adjustedProduce: request.milkOperations?.produce || 0,
          adjustedConsume: request.milkOperations?.consume || 0
        };

        // 验证GEM增量（如果提供了）
        if (requestedGems > 0) {
          gemValidation = await this.validateGemIncrement(
            requestedGems,
            resourceLimits.timeElapsedHours,
            walletId,
            transaction,
            language
          );
        }

        // 验证牛奶操作（如果提供了）
        if (request.milkOperations) {
          milkValidation = await this.validateMilkOperations(
            request.milkOperations,
            resourceLimits.timeElapsedHours,
            walletId,
            transaction,
            language
          );
        }

        // 应用验证结果
        if (!gemValidation.isValid || !milkValidation.isValid) {
          usedSystemCalculation = true;
          const reasons = [];
          if (!gemValidation.isValid && 'reason' in gemValidation) reasons.push(`GEM: ${gemValidation.reason}`);
          if (!milkValidation.isValid && 'reason' in milkValidation) reasons.push(`牛奶: ${milkValidation.reason}`);
          systemCalculationReason = reasons.join('; ');
        }

        finalTempGem = gemValidation.adjustedTempGem || 0;
        // 牛奶操作现在分别处理produce和consume
        if (milkValidation) {
          milkIncreased = formatToThreeDecimalsNumber(milkValidation.adjustedProduce);
          milkDecreased = formatToThreeDecimalsNumber(milkValidation.adjustedConsume);
        }
      }

      // 处理GEM更新（GEM只能增加，不能减少）
      // 如果用户提供了GEM请求，使用验证后的值；否则使用协调计算的值
      let actualGemIncrease = 0;
      if (requestedGems > 0) {
        // 用户提供了GEM请求，使用验证后的值
        actualGemIncrease = finalTempGem;
      } else {
        // 用户没有提供GEM请求，使用协调计算的自动产出
        // 这个值会在后面的协调计算中设置
      }

      if (actualGemIncrease > 0) {
        const currentGemBN = createBigNumber(wallet.gem || 0);
        const gemIncrementBN = createBigNumber(actualGemIncrease);
        const newGemBN = currentGemBN.plus(gemIncrementBN);

        console.log(`[GEM更新-用户请求] 用户${walletId}: 当前GEM=${currentGemBN.toFixed(3)}, 增加=${gemIncrementBN.toFixed(3)}, 新值=${newGemBN.toFixed(3)}`);

        let updateType = usedSystemCalculation ? 'SYSTEM_CALCULATION' : 'BATCH_UPDATE';
        let reference = usedSystemCalculation
          ? (language === 'en' ? 'System Calculated Update' : '系统计算更新')
          : (language === 'en' ? 'Batch Resource Update' : '批量资源更新');

        // 更新GEM（只增加）- 使用字符串保持精度
        wallet.gem = newGemBN.toFixed(3);
        await wallet.save({ transaction });

        // GEM的details已经在前面通过协调计算设置，这里不需要重复设置

        // 创建GEM历史记录（使用实际给用户的数量）
        const actualGemAdded = formatToThreeDecimalsNumber(gemIncrementBN);
        await WalletHistory.create({
          userId,
          walletId,
          currency: 'GEM',
          amount: actualGemAdded,
          reference,
          action: 'IN',
          category: updateType,
          credit_type: updateType,
          fe_display_remark: language === 'en'
            ? `Added ${actualGemAdded} GEM${usedSystemCalculation ? ' - System calculated due to unreasonable request' : ''}`
            : `增加了 ${actualGemAdded} GEM${usedSystemCalculation ? ' - 因请求不合理使用系统计算值' : ''}`,
          developer_remark: `批量资源更新: +${actualGemAdded} GEM${usedSystemCalculation ? ' [系统计算]' : ''}`
        }, { transaction });
      }

      // 先设置默认的details值，稍后会根据实际执行结果更新
      // 注意：离线用户的增减量会在后面的逻辑中根据实际情况计算

      // 处理PendingMilk更新（基于验证后的produce和consume值）
      const netMilkChange = milkValidation.adjustedProduce - milkValidation.adjustedConsume;
      if (netMilkChange !== 0) {
        // 每个用户都有出货线，如果查询失败则抛出错误
        if (!deliveryLine) {
          throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
        }
        const currentPendingMilkBN = createBigNumber(deliveryLine.pendingMilk || 0);
        const milkIncrementBN = createBigNumber(netMilkChange);
        const newPendingMilkBN = currentPendingMilkBN.plus(milkIncrementBN);

        let updateType = usedSystemCalculation ? 'SYSTEM_CALCULATION' : 'BATCH_UPDATE';
        let reference = usedSystemCalculation
          ? (language === 'en' ? 'System Calculated Update' : '系统计算更新')
          : (language === 'en' ? 'Batch Resource Update' : '批量资源更新');

        // 计算实际的牛奶变化量
        let actualMilkChange: number;

        // 确保PendingMilk不会变成负数
        if (newPendingMilkBN.isNegative()) {
          deliveryLine.pendingMilk = 0;
          actualMilkChange = formatToThreeDecimalsNumber(currentPendingMilkBN.negated());
        } else {
          deliveryLine.pendingMilk = formatToThreeDecimalsNumber(newPendingMilkBN);
          actualMilkChange = formatToThreeDecimalsNumber(milkIncrementBN);
        }

        // 牛奶的details已经在前面通过协调计算设置，这里不需要重复设置

        await deliveryLine.save({ transaction });

        // 创建PendingMilk历史记录
        await WalletHistory.create({
          userId,
          walletId,
          currency: 'MILK',
          amount: Math.abs(actualMilkChange),
          reference,
          action: actualMilkChange >= 0 ? 'IN' : 'OUT',
          category: updateType,
          credit_type: updateType,
          fe_display_remark: language === 'en'
            ? `${actualMilkChange >= 0 ? 'Added' : 'Reduced'} ${Math.abs(actualMilkChange)} pending milk${usedSystemCalculation ? ' - System calculated due to unreasonable request' : ''}`
            : `${actualMilkChange >= 0 ? '增加了' : '减少了'} ${Math.abs(actualMilkChange)} 待处理牛奶${usedSystemCalculation ? ' - 因请求不合理使用系统计算值' : ''}`,
          developer_remark: `批量资源更新: ${actualMilkChange >= 0 ? '+' : ''}${actualMilkChange} 待处理牛奶${usedSystemCalculation ? ' [系统计算]' : ''}`
        }, { transaction });
      }

      // 在事务提交前计算所有需要的数据
      const now = new Date();

      // 根据实际执行结果设置details（显示实际可执行的变化量）
      if (isInValidTimeWindow) {
        // 在线用户：计算实际的资源变化（使用BigNumber保持精度）
        const currentGemBN = createBigNumber(wallet.gem || 0);
        const actualGemChangeBN = currentGemBN.minus(beforeUpdateBN.gem);
        const actualGemChange = formatToThreeDecimalsNumber(actualGemChangeBN);

        // 设置GEM的实际增量（格式化为3位小数）
        gemIncreased = formatToThreeDecimalsNumber(Math.max(0, actualGemChange));

        // 使用协调计算来获取考虑时间依赖关系的准确数值
        const coordinatedResult = await this.calculateCoordinatedProduction(
          walletId,
          timeElapsedSeconds,
          beforeUpdate.pendingMilk,
          transaction
        );

        // 设置牛奶的实际增减量（格式化为3位小数）
        milkIncreased = formatToThreeDecimalsNumber(coordinatedResult.farmProduced);
        milkDecreased = formatToThreeDecimalsNumber(coordinatedResult.deliveryConsumed);

        // 使用协调计算的GEM产出结果用于显示（这是理论上应该产出的量）
        gemIncreased = formatToThreeDecimalsNumber(coordinatedResult.gemProduced);

        // 根据协调计算结果更新pendingMilk
        if (deliveryLine) {
          deliveryLine.pendingMilk = formatToThreeDecimalsNumber(coordinatedResult.finalPendingMilk);
          await deliveryLine.save({ transaction });
        }

        // 如果用户没有提供GEM请求，使用协调计算的自动产出更新GEM
        if (requestedGems === 0 && coordinatedResult.gemProduced > 0) {
          const currentGemBN = createBigNumber(wallet.gem || 0);
          const gemIncrementBN = createBigNumber(coordinatedResult.gemProduced);
          const newGemBN = currentGemBN.plus(gemIncrementBN);

          console.log(`[GEM自动更新] 用户${walletId}: 当前GEM=${currentGemBN.toFixed(3)}, 增加=${gemIncrementBN.toFixed(3)}, 新值=${newGemBN.toFixed(3)}`);

          // 直接使用BigNumber的字符串表示，保持精度
          wallet.gem = newGemBN.toFixed(3);
          await wallet.save({ transaction });

          // 创建GEM历史记录
          await WalletHistory.create({
            userId,
            walletId,
            currency: 'GEM',
            amount: formatToThreeDecimalsNumber(gemIncrementBN),
            reference: language === 'en' ? 'Auto Production Update' : '自动产出更新',
            action: 'IN',
            category: 'AUTO_PRODUCTION',
            credit_type: 'AUTO_PRODUCTION',
            fe_display_remark: language === 'en'
              ? `Auto produced ${formatToThreeDecimalsNumber(gemIncrementBN)} GEM`
              : `自动产出 ${formatToThreeDecimalsNumber(gemIncrementBN)} GEM`,
            developer_remark: `自动产出: +${formatToThreeDecimalsNumber(gemIncrementBN)} GEM`
          }, { transaction });
        } else {
          console.log(`[GEM更新跳过] 用户${walletId}: requestedGems=${requestedGems}, coordinatedResult.gemProduced=${coordinatedResult.gemProduced}`);
        }

        // 检查是否有实际的数据库变化，如果没有则显示理论计算值（使用BigNumber保持精度）
        const finalCurrentGemBN = createBigNumber(wallet.gem || 0);
        const finalGemChangeBN = finalCurrentGemBN.minus(beforeUpdateBN.gem);
        const finalGemChange = formatToThreeDecimalsNumber(finalGemChangeBN);

        const finalCurrentMilkBN = createBigNumber(deliveryLine ? deliveryLine.pendingMilk || 0 : 0);
        const finalMilkChangeBN = finalCurrentMilkBN.minus(beforeUpdateBN.pendingMilk);
        const finalMilkChange = formatToThreeDecimalsNumber(finalMilkChangeBN);

        console.log(`[GEM变化检查] 用户${walletId}: finalGemChange=${finalGemChange}, finalMilkChange=${finalMilkChange}`);

        // 只有在没有实际数据库变化时才使用理论值覆盖
        if (finalGemChange === 0 && finalMilkChange === 0) {
          const currentPendingMilk = beforeUpdate.pendingMilk;
          const theoreticalResult = await this.calculateCoordinatedProduction(
            walletId,
            timeElapsedSeconds,
            currentPendingMilk,
            transaction
          );

          // 使用理论计算值（格式化为3位小数）
          milkIncreased = formatToThreeDecimalsNumber(theoreticalResult.farmProduced);
          milkDecreased = formatToThreeDecimalsNumber(theoreticalResult.deliveryConsumed);
          gemIncreased = formatToThreeDecimalsNumber(theoreticalResult.gemProduced);
        }
      } else {
        // 离线用户：不显示任何增减量
        milkIncreased = 0;
        milkDecreased = 0;
        gemIncreased = 0;
      }

      // 统一更新lastActiveTime
      wallet.lastActiveTime = now;
      await wallet.save({ transaction });

      // 在事务提交前获取基础配置信息
      const basicConfigRates = await this.getBasicConfigurationRates(walletId, transaction, timeElapsedSeconds);

      // 构建响应数据
      // 每个用户都有出货线，如果查询失败则抛出错误
      if (!deliveryLine) {
        throw new Error(`DeliveryLine not found for walletId: ${walletId}`);
      }

      // 计算实际的数据库变化量（使用BigNumber保持精度）
      const actualGemChangeBN = createBigNumber(wallet.gem || 0).minus(beforeUpdateBN.gem);
      const actualGemChange = formatToThreeDecimalsNumber(actualGemChangeBN);

      const actualMilkChangeBN = createBigNumber(deliveryLine ? deliveryLine.pendingMilk || 0 : 0).minus(beforeUpdateBN.pendingMilk);
      const actualMilkChange = formatToThreeDecimalsNumber(actualMilkChangeBN);

      const afterUpdate = {
        gem: formatToThreeDecimalsNumber(wallet.gem || 0),
        pendingMilk: formatToThreeDecimalsNumber(deliveryLine.pendingMilk || 0),
        lastActiveTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
      };

      // 提交事务
      await transaction.commit();

      // 构建 changes 对象，包含实际变化量和理论计算值
      const changesData: any = {
        ...(usedSystemCalculation && {
          usedSystemCalculation: true,
          systemCalculationReason
        }),
        productionRates: basicConfigRates,
        // 实际的数据库变化量
        actual: {
          gem: {
            changed: actualGemChange
          },
          milk: {
            changed: actualMilkChange
          }
        },
        // 理论计算的游戏机制产出量
        details: {
          gem: {
            increased: gemIncreased
          },
          milk: {
            increased: milkIncreased,
            decreased: milkDecreased
          }
        }
      };

      return {
        success: true,
        data: {
          beforeUpdate,
          afterUpdate,
          changes: changesData,
          timestamp: dayjs(now).format('YYYY-MM-DD HH:mm:ss')
        },
        message: language === 'en' ? 'Resources updated successfully' : '资源更新成功'
      };

    } catch (error) {
      // 只有在事务还没有提交时才尝试回滚
      try {
        await transaction.rollback();
      } catch (rollbackError) {
        // 如果回滚失败（比如事务已经提交），忽略这个错误
        console.warn('事务回滚失败，可能事务已经提交:', rollbackError);
      }
      throw error;
    }
  }
}

export default BatchResourceUpdateService;
