// src/app.ts
import express from "express";
import bodyParser from "body-parser";
import dotenv from "dotenv";
import { connectDB, sequelize } from "./config/db";
import { redis } from "./config/redis";
// 不再需要子进程相关导入
import path from 'path';
import cors from "cors";
import cron from "node-cron";
import { Queue } from 'bullmq';
import { Server } from 'http'; // 导入 Server 类型

// 路由导入
import userRoutes from "./routes/userRoutes";
import chestRoutes from "./routes/chestRoutes";
import referralRoutes from "./routes/referralRoutes";
import dailyClaimRoutes from "./routes/dailyClaimRoutes";
import taskRoutes from "./routes/taskRoutes";
import ticketRoutes from "./routes/ticketRoutes";
import freeTicketTransferRoutes from "./routes/freeTicketTransferRoutes";
import walletRoutes from "./routes/walletRoutes";
import web3AuthRoutes from "./routes/web3AuthRoutes";
import gameRouter from "./routes/game";
import reservationRouter from "./routes/reservation";
import resetRouter from "./routes/reset";
import my_reservationsRouter from "./routes/my_reservations";
import rewardsRouter from "./routes/rewards";
import { router as tonProofRouter } from "./routes/tonProof";
import bullKingRoutes from "./routes/bullKingRoutes";
import kolProgressRoutes from "./routes/kolProgressRoutes";
import kolRoutes from "./routes/kolRoutes";
import web3SignTestRoutes from "./routes/web3SignTestRoutes";
import rebateRoutes from "./routes/rebateRoutes";
import bullUnlockRoutes from "./routes/bullUnlockRoutes";
import withdrawalRoutes from "./routes/withdrawalRoutes";
import jackpotChestRoutes from "./routes/jackpotChestRoutes";
import telegramShareRoutes from "./routes/telegramShareRoutes";
import telegramPaymentRoutes from "./routes/telegramPaymentRoutes";
import fragmentRoutes from "./routes/fragmentRoutes";
import testChestRoutes from "./routes/testChestRoutes";
import gemLeaderboardRoutes from "./routes/gemLeaderboardRoutes";
import farmPlotRoutes from "./routes/farmPlotRoutes";
import deliveryLineRoutes from "./routes/deliveryLineRoutes";
import gameLoopRoutes from "./routes/gameLoopRoutes";
import dappPortalPaymentRoutes from "./routes/dappPortalPaymentRoutes";
import iapRoutes from './routes/iapRoutes';
import testResetRoutes from './routes/testResetRoutes';

// 服务和工具导入
import { scheduleDailyRoundJobs } from "./jobs/scheduleDailyRoundJobs";
import { scheduleKaiaPriceUpdateJob } from "./jobs/scheduleKaiaPriceUpdateJob";
import { WalletHistory } from "./models/WalletHistory";
import TonCenterClient from "./services/tonService";
import { scheduleDailySessions } from "./scheduler/dailySessions";
import { startPaymentStatusUpdater, stopPaymentStatusUpdater } from "./scheduler/paymentStatusUpdater";
import { generateRandomNumber } from "./utils/random";

import { integrateTonWithdrawal } from "./jobs/integrateTonWithdrawal";

 // 应用语言中间件
 import { languageMiddleware } from "./middlewares/languageMiddleware";


dotenv.config();

// 初始化 TonCenter 客户端并获取区块信息
const initTonClient = async () => {
  const apiKey = process.env.TONCENTER_API_KEY;
  if (!apiKey) {
    console.warn("TONCENTER_API_KEY not found in environment variables");
    return;
  }
  
  const tonClient = new TonCenterClient(apiKey);
  
  try {
    const blockNumber = await tonClient.getLatestBlockNumber();
    const winerNum = await generateRandomNumber(blockNumber);
    
    console.log("winerNum:", winerNum);
    console.log("Latest block number:", blockNumber);
  } catch (error) {
    console.error("Failed to get block information:", error);
  }
};

// 直接导入工作进程模块，不再使用子进程
import * as lotteryResultWorker from './jobs/lotteryResultWorker';
import * as moofHoldersRewardWorker from './jobs/moofHoldersRewardWorker';
import * as personalKolRewardWorker from './jobs/personalKolRewardWorker';
import * as teamKolRewardWorker from './jobs/teamKolRewardWorker';
import * as dailyRebateSettlementWorker from './jobs/dailyRebateSettlementWorker';
import * as jackpotChestWorker from './jobs/jackpotChestWorker';
import * as withdrawalWorker from './jobs/withdrawalWorker';
import * as kaiaPriceUpdateWorker from './jobs/kaiapriceUpdateWorker';


// 设置定时任务
const setupCronJobs = (moofHoldersRewardQueue: Queue, personalKolRewardQueue: Queue, teamKolRewardQueue: Queue, dailyRebateSettlementQueue: Queue) => {
  // 存储所有定时任务，以便在关闭时停止
  const scheduledTasks: cron.ScheduledTask[] = [];
  
  // 启动支付状态更新定时任务
  const paymentStatusTask = startPaymentStatusUpdater();
  paymentStatusTask.start();
  scheduledTasks.push(paymentStatusTask);
  
  // MOOF 持有者奖励 - 每周日晚上 23:59
  const moofTask = cron.schedule('59 23 * * 0', async () => {
    try {
      await moofHoldersRewardQueue.add(
        'weekly-distribution',
        {},
        {
          removeOnComplete: true,
          removeOnFail: 1000,
        }
      );
      console.log('已添加本周 MOOF 持有者奖励分发任务');
    } catch (error) {
      console.error('添加 MOOF 持有者奖励任务失败:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Shanghai"
  });
  scheduledTasks.push(moofTask);
  
  // 个人 KOL 奖励 - 每周一凌晨 00:30
  const personalKolTask = cron.schedule('30 0 * * 1', async () => {
    try {
      await personalKolRewardQueue.add(
        'weekly-distribution',
        {},
        {
          removeOnComplete: true,
          removeOnFail: 1000,
        }
      );
      console.log('已添加本周个人 KOL 奖励分发任务');
    } catch (error) {
      console.error('添加个人 KOL 奖励任务失败:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Shanghai"
  });
  scheduledTasks.push(personalKolTask);
  
  // 团队 KOL 奖励 - 每周一凌晨 01:00
  const teamKolTask = cron.schedule('0 1 * * 1', async () => {
    try {
      await teamKolRewardQueue.add(
        'weekly-distribution',
        {},
        {
          removeOnComplete: true,
          removeOnFail: 1000,
        }
      );
      console.log('已添加本周团队 KOL 奖励分发任务');
    } catch (error) {
      console.error('添加团队 KOL 奖励任务失败:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Shanghai"
  });
  scheduledTasks.push(teamKolTask);
  
  // 每日返利结算 - 每天12:00
  const rebateTask = cron.schedule('0 12 * * *', async () => {
    try {
      await dailyRebateSettlementQueue.add(
        'daily-settlement',
        {},
        {
          removeOnComplete: true,
          removeOnFail: 1000,
        }
      );
      console.log('已添加每日返利结算任务');
    } catch (error) {
      console.error('添加每日返利结算任务失败:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Shanghai"
  });
  scheduledTasks.push(rebateTask);
  
  // 返回所有定时任务，以便在关闭时停止
  return scheduledTasks;
};

// 优雅关闭服务
async function gracefulShutdown(
  serverInstance: Server | null, // 接收 Server 实例
  workers: { [key: string]: any }, // 现在只是一个空对象，不再存储子进程
  queues: { [key: string]: Queue }, // 接收 Queue 实例
  cronTasks: cron.ScheduledTask[], // 接收定时任务
  reason: string
) {
  console.log(`收到 ${reason}，正在关闭服务...`);
  try {
    // 0. 停止所有定时任务
    console.log('正在停止所有定时任务...');
    cronTasks.forEach(task => {
      try {
        task.stop();
      } catch (err) {
        console.error('停止定时任务失败:', err);
      }
    });
    
    // 停止支付状态更新任务
    const paymentStatusTask = cronTasks.find(task => task.toString().includes('*/5 * * * *'));
    if (paymentStatusTask) {
      stopPaymentStatusUpdater(paymentStatusTask);
    }
    console.log('所有定时任务已停止');
    
    // 0.1 移除所有重复任务
    console.log('正在移除所有重复任务...');
    const removeRepeatablePromises = Object.entries(queues).map(async ([name, queue]) => {
      try {
        const repeatableJobs = await queue.getJobSchedulers();
        const removePromises = repeatableJobs.map(job => 
          queue.removeJobScheduler(job.name)
            .catch(err => console.error(`移除重复任务 ${job.id} 失败:`, err))
        );
        await Promise.allSettled(removePromises);
        console.log(`${name} 队列的重复任务已移除`);
      } catch (err) {
        console.error(`获取 ${name} 队列的重复任务失败:`, err);
      }
    });
    await Promise.allSettled(removeRepeatablePromises);
    console.log('所有重复任务已移除');

    // 1. 关闭 HTTP 服务器，停止接受新连接
    if (serverInstance) {
      await new Promise<void>((resolve) => {
        try {
          // 检查服务器是否正在监听
          if (serverInstance.listening) {
            serverInstance.close((err) => {
              if (err) {
                console.error('关闭 HTTP 服务器时出错:', err);
                // 即使出错也继续关闭流程
              }
              console.log('HTTP 服务器已关闭');
              resolve();
            });
          } else {
            console.log('HTTP 服务器未在监听状态，无需关闭');
            resolve();
          }
        } catch (err) {
          console.error('尝试关闭 HTTP 服务器时发生异常:', err);
          // 即使出现异常也继续关闭流程
          resolve();
        }
      });
    } else {
       console.log('HTTP 服务器未运行或已关闭');
    }

    // 2. 关闭所有 BullMQ 队列
    console.log('正在关闭 BullMQ 队列...');
    const queueClosePromises = Object.entries(queues).map(([name, queue]) =>
      queue.close().then(() => console.log(`${name} 队列已关闭`)).catch(err => console.error(`关闭 ${name} 队列失败:`, err))
    );
    await Promise.allSettled(queueClosePromises);
    console.log('所有 BullMQ 队列已关闭');

    // 3. 关闭 Redis 连接
    try {
       await redis.quit();
       console.log('Redis 连接已关闭');
    } catch (error) {
       console.error('关闭 Redis 连接失败:', error);
    }

    // 4. 关闭数据库连接 
    try {
      await sequelize.close();
      console.log('数据库连接已关闭');
    } catch (error) {
      console.error('关闭数据库连接失败:', error);
    }

    console.log('所有资源已清理完毕，正在退出...');
    
    // 设置最终退出超时，确保进程一定会退出
    setTimeout(() => {
      console.warn('进程未能在预期时间内退出，强制退出');
      process.exit(0);
    }, 3000);
    
    process.exit(0);
  } catch (error) {
    console.error('关闭服务时发生严重错误:', error);
    // 即使出现严重错误，也尝试强制关闭关键资源
    try {
      // 尝试关闭数据库和Redis连接
      await Promise.allSettled([
        sequelize.close().catch(e => console.error("强制关闭数据库失败:", e)),
        redis.quit().catch(e => console.error("强制关闭Redis失败:", e))
      ]);
    } catch (e) {
      console.error("最终清理资源失败:", e);
    }
    process.exit(1);
  }
}


async function main() {
  // 初始化 TonCenter 客户端
  await initTonClient();

  const app = express();
  let serverInstance: Server | null = null; // 用于存储 Server 实例
  const workers: { [key: string]: any } = {}; // 不再使用子进程，但保留变量以兼容现有代码
  const queues: { [key: string]: Queue } = {}; // 用于存储队列实例

  // 中间件设置
  app.use(cors());
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));
  app.use(languageMiddleware);
 

  // 路由设置
  app.use("/api/user", userRoutes);
  app.use("/api/chest", chestRoutes);
  app.use("/api/referral", referralRoutes);
  app.use("/api/invite", dailyClaimRoutes);
  app.use("/api/ton-proof", tonProofRouter);
  app.use("/api/tasks", taskRoutes);
  app.use("/api/ticket", ticketRoutes);
  app.use("/api/wallet", walletRoutes);
  app.use("/api/game", gameRouter);
  app.use("/api/game", reservationRouter);
  app.use("/api/game", my_reservationsRouter);
  app.use("/api/reset", resetRouter);
  app.use("/api/rewards", rewardsRouter);
  app.use("/api/bull-king", bullKingRoutes);
  app.use("/api/kol-progress", kolProgressRoutes);
  app.use("/api/kol", kolRoutes);
  app.use("/api/rebate", rebateRoutes);
  app.use("/api/bull-unlock", bullUnlockRoutes);
  app.use("/api/withdrawal", withdrawalRoutes);
  app.use("/api/jackpot-chest", jackpotChestRoutes);
  app.use("/api/telegram-share", telegramShareRoutes);
  app.use("/api/telegram-payment", telegramPaymentRoutes);
  app.use("/api/fragment", fragmentRoutes);
  app.use("/api/free-ticket", freeTicketTransferRoutes);
  app.use("/api/test-chest", testChestRoutes);
  app.use("/api/gem-leaderboard", gemLeaderboardRoutes);
  app.use("/api/web3-auth", web3AuthRoutes);
  app.use("/api/farm", farmPlotRoutes);
app.use("/api/delivery", deliveryLineRoutes);
app.use("/api/game-loop", gameLoopRoutes);
  app.use("/api/web3-sign-test", web3SignTestRoutes);
  app.use("/api/dapp-portal-payment", dappPortalPaymentRoutes);
app.use('/api/iap', iapRoutes);
  // 测试重置路由（仅在开发环境下可用）
  app.use('/api/test', testResetRoutes);

  // 数据库连接
  try {
    await connectDB();
    await sequelize.sync(); // 根据模型自动建表(开发模式可用,生产请谨慎)
    console.log('数据库连接成功');
  } catch (error) {
    console.error('数据库连接失败:', error);
    process.exit(1);
  }

  // Redis 测试
  try {
    await redis.set("wolffun_test", "hello");
    const val = await redis.get("wolffun_test");
    console.log("Redis test value:", val);
  } catch (error) {
    console.error('Redis 测试失败:', error);
  }

  // 存储所有定时任务
  const cronTasks: cron.ScheduledTask[] = [];

  // 启动 HTTP 服务器 (第一次，保留这个) 
  const port = process.env.PORT || 3000;
  serverInstance = app.listen(port, () => { // 保存实例
    console.log(`Wolf.Fun game server running on port ${port}...`);
  });
  serverInstance.on('error', (error) => { // 添加服务器错误处理
      console.error('HTTP 服务器错误:', error);
      gracefulShutdown(serverInstance, workers, queues, cronTasks, '服务器错误');
  });

  // 启动每日 Session 预生成任务
  try {
    scheduleDailySessions();
    console.log("每日 Session 预生成任务已启动。");
  } catch (error) {
    console.error('启动每日 Session 预生成任务失败:', error);
  }

  // 调度当天所有场次的抽奖任务
  try {
    await scheduleDailyRoundJobs();
    console.log("所有抽奖任务已调度");
  } catch (err) {
    console.error("调度抽奖任务失败：", err);
  }

  // 调度Kaia价格更新任务
  try {
    await scheduleKaiaPriceUpdateJob();
    console.log("Kaia价格更新任务已调度");
  } catch (err) {
    console.error("调度Kaia价格更新任务失败：", err);
  }

  // 配置队列
  try {
    // 不再需要启动工作进程，直接使用导入的模块
    console.log('正在初始化队列和处理器...');



    // 创建奖励队列并存储
    queues.moofHoldersRewardQueue = new Queue('moof-holders-reward-job', { connection: redis });
    queues.personalKolRewardQueue = new Queue('personal-kol-reward-job', { connection: redis });
    queues.teamKolRewardQueue = new Queue('team-kol-reward-job', { connection: redis });
    queues.dailyRebateSettlementQueue = new Queue('daily-rebate-settlement-job', { connection: redis });
    // 创建 Jackpot 队列并存储
    queues.jackpotChestQueue = new Queue('jackpot-chest-queue', { connection: redis });
    // 创建提现队列
    queues.withdrawalQueue = new Queue('withdrawal-queue', { connection: redis });
    // 创建KAIA价格更新队列
    queues.kaiaPriceUpdateQueue = new Queue('kaia-price-update-job', { connection: redis });
    
    // 初始化处理器
    console.log('正在初始化各模块处理器...');
    
    // 初始化抽奖结果处理器
    if (lotteryResultWorker.initializeWorker) {
      await lotteryResultWorker.initializeWorker(queues);
      console.log('抽奖结果处理器已初始化');
    }
    
    // 初始化 MOOF 持有者奖励处理器
    if (moofHoldersRewardWorker.initializeWorker) {
      await moofHoldersRewardWorker.initializeWorker(queues.moofHoldersRewardQueue);
      console.log('MOOF 持有者奖励处理器已初始化');
    }
    
    // 初始化个人 KOL 奖励处理器
    if (personalKolRewardWorker.initializeWorker) {
      await personalKolRewardWorker.initializeWorker(queues.personalKolRewardQueue);
      console.log('个人 KOL 奖励处理器已初始化');
    }
    
    // 初始化团队 KOL 奖励处理器
    if (teamKolRewardWorker.initializeWorker) {
      await teamKolRewardWorker.initializeWorker(queues.teamKolRewardQueue);
      console.log('团队 KOL 奖励处理器已初始化');
    }
    
    // 初始化每日返利结算处理器
    if (dailyRebateSettlementWorker.initializeWorker) {
      await dailyRebateSettlementWorker.initializeWorker(queues.dailyRebateSettlementQueue);
      console.log('每日返利结算处理器已初始化');
    }
    
    // 初始化 Jackpot 宝箱处理器
    if (jackpotChestWorker.initializeWorker) {
      await jackpotChestWorker.initializeWorker(queues.jackpotChestQueue);
      console.log('Jackpot 宝箱处理器已初始化');
    }
    
    // 初始化提现处理器
    if (withdrawalWorker.initializeWorker) {
      await withdrawalWorker.initializeWorker(queues.withdrawalQueue);
      console.log('提现处理器已初始化');
    }
    
    // 初始化KAIA价格更新处理器
    if (kaiaPriceUpdateWorker.initializeWorker) {
      await kaiaPriceUpdateWorker.initializeWorker(queues.kaiaPriceUpdateQueue);
      console.log('KAIA价格更新处理器已初始化');
    }
    
    // 设置定时任务
    setupCronJobs(
        queues.moofHoldersRewardQueue,
        queues.personalKolRewardQueue,
        queues.teamKolRewardQueue,
        queues.dailyRebateSettlementQueue
    );

    // 初始化Jackpot奖池任务
    await queues.jackpotChestQueue.add('initialize-jackpot', {}, {
      removeOnComplete: true,
      removeOnFail: 1000
    });
    console.log('已添加 Jackpot 奖池初始化任务');

    // 设置 Jackpot 自动领取定时任务
    await queues.jackpotChestQueue.add('auto-collect-chests', {}, {
      repeat: {
        pattern: '* * * * *' // 每分钟执行一次
      },
      jobId: 'auto-collect-chests-job', // 给重复任务一个固定的ID，避免重复添加
      removeOnComplete: true,
      removeOnFail: 1000
    });
    console.log('已添加 Jackpot 自动领取定时任务');


  } catch (err) {
    console.error('启动工作进程或设置队列/任务时失败:', err);
       // 根据需要决定是否退出
       // await gracefulShutdown(serverInstance, workers, queues, '启动失败');
       // process.exit(1);
  }


  // 设置进程事件监听，传递 serverInstance 和 queues
  process.on('SIGTERM', () => gracefulShutdown(serverInstance, workers, queues, cronTasks, 'SIGTERM 信号'));
  process.on('SIGINT', () => gracefulShutdown(serverInstance, workers, queues, cronTasks, 'SIGINT 信号'));
  process.on('uncaughtException', async (error) => {
    console.error('未捕获的异常:', error);
    await gracefulShutdown(serverInstance, workers, queues, cronTasks, '未捕获的异常');
  });
  process.on('unhandledRejection', async (reason, promise) => {
    console.error('未处理的 Promise 拒绝:', reason);
    await gracefulShutdown(serverInstance, workers, queues, cronTasks, '未处理的 Promise 拒绝');
  });
}

main().catch(async (err) => { // main 函数本身的 catch 也应该尝试优雅关闭
    console.error("服务器启动失败:", err);
    // 注意：此时 serverInstance, workers, queues 可能未完全初始化
    // 可以尝试关闭已初始化的部分，或者直接退出
    await sequelize.close().catch(e => console.error("数据库关闭失败:", e));
    await redis.quit().catch(e => console.error("Redis 关闭失败:", e));
    process.exit(1);
});


