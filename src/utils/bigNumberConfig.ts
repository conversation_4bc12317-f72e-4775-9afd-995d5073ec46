// BigNumber.js 配置文件
// 用于农场区和出货线系统的高精度数值计算

import BigNumber from 'bignumber.js';

// 配置 BigNumber 全局设置
BigNumber.config({
  // 设置小数位数为3位
  DECIMAL_PLACES: 3,
  // 设置舍入模式为四舍五入
  ROUNDING_MODE: BigNumber.ROUND_HALF_UP,
  // 设置指数表示法的范围
  EXPONENTIAL_AT: [-7, 20],
  // 设置最大有效数字
  RANGE: [-1e9, 1e9],
  // 启用加密安全的随机数生成
  CRYPTO: false,
  // 设置模运算的精度
  MODULO_MODE: BigNumber.ROUND_DOWN,
  // 设置除零时的行为
  POW_PRECISION: 0,
  // 设置格式化选项
  FORMAT: {
    prefix: '',
    suffix: '',
    decimalSeparator: '.',
    groupSeparator: ',',
    groupSize: 3,
    secondaryGroupSize: 0,
    fractionGroupSeparator: ' ',
    fractionGroupSize: 0
  }
});

/**
 * 创建 BigNumber 实例的工厂函数
 * @param value 数值
 * @returns BigNumber 实例
 */
export function createBigNumber(value: number | string | BigNumber): BigNumber {
  return new BigNumber(value);
}

/**
 * 格式化数值为3位小数的字符串
 * @param value BigNumber 或数值
 * @returns 格式化后的字符串
 */
export function formatToThreeDecimals(value: number | string | BigNumber): string {
  const bn = value instanceof BigNumber ? value : new BigNumber(value);
  return bn.toFixed(3);
}

/**
 * 格式化数值为3位小数的数字
 * @param value BigNumber 或数值
 * @returns 格式化后的数字
 */
export function formatToThreeDecimalsNumber(value: number | string | BigNumber): number {
  const bn = value instanceof BigNumber ? value : new BigNumber(value);
  return parseFloat(bn.toFixed(3));
}

/**
 * 安全的幂运算
 * @param base 底数
 * @param exponent 指数
 * @returns BigNumber 结果
 */
export function safePow(base: number | string | BigNumber, exponent: number | string | BigNumber): BigNumber {
  const baseBN = base instanceof BigNumber ? base : new BigNumber(base);
  const expBN = exponent instanceof BigNumber ? exponent : new BigNumber(exponent);
  return baseBN.pow(expBN);
}

/**
 * 安全的乘法运算
 * @param a 乘数1
 * @param b 乘数2
 * @returns BigNumber 结果
 */
export function safeMultiply(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.multipliedBy(bBN);
}

/**
 * 安全的除法运算
 * @param a 被除数
 * @param b 除数
 * @returns BigNumber 结果
 */
export function safeDivide(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.dividedBy(bBN);
}

/**
 * 安全的加法运算
 * @param a 加数1
 * @param b 加数2
 * @returns BigNumber 结果
 */
export function safeAdd(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.plus(bBN);
}

/**
 * 安全的减法运算
 * @param a 被减数
 * @param b 减数
 * @returns BigNumber 结果
 */
export function safeSubtract(a: number | string | BigNumber, b: number | string | BigNumber): BigNumber {
  const aBN = a instanceof BigNumber ? a : new BigNumber(a);
  const bBN = b instanceof BigNumber ? b : new BigNumber(b);
  return aBN.minus(bBN);
}

/**
 * 农场区专用计算函数
 */
export class FarmPlotCalculator {
  /**
   * 计算基础产量：基础产量 × (1.5)^(等级-1)
   * 注意：基础产量由牧场区编号决定，编号1=1，编号2=2，编号3=4...
   * @param level 等级
   * @param plotNumber 牧场区编号（可选，如果不提供则默认为1）
   * @returns 基础产量
   */
  static calculateBaseProduction(level: number, plotNumber: number = 1): number {
    // 根据文档：解锁产量提升：每次解锁 基础产量×2.0倍
    // 牧场区#1基础产量=1，牧场区#2基础产量=2，牧场区#3基础产量=4...
    const plotBaseProduction = createBigNumber(Math.pow(2.0, plotNumber - 1));
    const levelMultiplier = createBigNumber(1.5);
    const levelExponent = createBigNumber(level - 1);
    const result = plotBaseProduction.multipliedBy(safePow(levelMultiplier, levelExponent));
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算生产速度：当前速度 ÷ 1.05
   * @param currentSpeed 当前速度
   * @returns 新的生产速度
   */
  static calculateUpgradedSpeed(currentSpeed: number): number {
    const current = createBigNumber(currentSpeed);
    const divisor = createBigNumber(1.05);
    const result = current.dividedBy(divisor);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算升级费用：当前费用 × 1.5
   * @param currentCost 当前费用
   * @returns 新的升级费用
   */
  static calculateUpgradeCost(currentCost: number): number {
    const current = createBigNumber(currentCost);
    const multiplier = createBigNumber(1.5);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 根据等级计算升级费用：200 × (1.5)^(等级-1)
   * @param level 当前等级
   * @returns 升级费用
   */
  static calculateUpgradeCostByLevel(level: number): number {
    const baseCost = createBigNumber(200);
    const multiplier = createBigNumber(1.5);
    const exponent = createBigNumber(level - 1);
    const result = baseCost.multipliedBy(safePow(multiplier, exponent));
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算解锁费用：2000 × 2^(编号-2)
   * @param plotNumber 农场区编号
   * @returns 解锁费用
   */
  static calculateUnlockCost(plotNumber: number): number {
    if (plotNumber === 1) return 0;
    
    const base = createBigNumber(2000);
    const multiplier = createBigNumber(2);
    const exponent = createBigNumber(plotNumber - 2);
    const result = base.multipliedBy(safePow(multiplier, exponent));
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算产量提升：当前产量 × 1.5
   * @param currentProduction 当前产量
   * @returns 新的产量
   */
  static calculateUpgradedProduction(currentProduction: number): number {
    const current = createBigNumber(currentProduction);
    const multiplier = createBigNumber(1.5);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }
}

/**
 * 出货线专用计算函数
 */
export class DeliveryLineCalculator {
  /**
   * 计算出货速度：当前速度 ÷ 1.01
   * @param currentSpeed 当前速度
   * @returns 新的出货速度
   */
  static calculateUpgradedSpeed(currentSpeed: number): number {
    const current = createBigNumber(currentSpeed);
    const divisor = createBigNumber(1.01);
    const result = current.dividedBy(divisor);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算方块容量：当前容量 × 2.0
   * @param currentUnit 当前容量
   * @returns 新的方块容量
   */
  static calculateUpgradedUnit(currentUnit: number): number {
    const current = createBigNumber(currentUnit);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算方块价格：当前价格 × 2.0
   * @param currentPrice 当前价格
   * @returns 新的方块价格
   */
  static calculateUpgradedPrice(currentPrice: number): number {
    const current = createBigNumber(currentPrice);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }

  /**
   * 计算升级费用：当前费用 × 2.0
   * @param currentCost 当前费用
   * @returns 新的升级费用
   */
  static calculateUpgradeCost(currentCost: number): number {
    const current = createBigNumber(currentCost);
    const multiplier = createBigNumber(2.0);
    const result = current.multipliedBy(multiplier);
    return formatToThreeDecimalsNumber(result);
  }
}

export default BigNumber;
